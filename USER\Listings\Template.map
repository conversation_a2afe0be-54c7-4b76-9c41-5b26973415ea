Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to display.o(i.Display_Heart) for Display_Heart
    main.o(i.main) refers to pwm.o(i.BUCK_SDIO_Init) for BUCK_SDIO_Init
    main.o(i.main) refers to adc.o(i.ADCx_InitConfig) for ADCx_InitConfig
    main.o(i.main) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    main.o(i.main) refers to pwm.o(i.TIM1_PWM_Init) for TIM1_PWM_Init
    main.o(i.main) refers to display.o(i.Normal_Display) for Normal_Display
    main.o(i.main) refers to core.o(.data) for OLEDShowCnt
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_ld.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_ld.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_ld.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_ld.o(RESET) refers to startup_stm32f10x_ld.o(STACK) for __initial_sp
    startup_stm32f10x_ld.o(RESET) refers to startup_stm32f10x_ld.o(.text) for Reset_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_ld.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_ld.o(RESET) refers to encoder.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_ld.o(RESET) refers to encoder.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_ld.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_ld.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_ld.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_ld.o(.text) refers to startup_stm32f10x_ld.o(HEAP) for Heap_Mem
    startup_stm32f10x_ld.o(.text) refers to startup_stm32f10x_ld.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    adc.o(i.ADC1_IRQHandler) refers to stm32f10x_adc.o(i.ADC_ClearITPendingBit) for ADC_ClearITPendingBit
    adc.o(i.ADC1_IRQHandler) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.ADCx_InitConfig) refers to adc.o(i.RCC_Configuration) for RCC_Configuration
    adc.o(i.ADCx_InitConfig) refers to adc.o(i.GPIO_Configuration) for GPIO_Configuration
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_dma.o(i.DMA_Init) for DMA_Init
    adc.o(i.ADCx_InitConfig) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.ADCx_InitConfig) refers to adc.o(.bss) for ADCConvertedValue
    adc.o(i.GPIO_Configuration) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.RCC_Configuration) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.RCC_Configuration) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    adc.o(i.RCC_Configuration) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    core.o(i.Buck_StateM) refers to core.o(i.Buck_StateMInit) for Buck_StateMInit
    core.o(i.Buck_StateM) refers to core.o(i.Buck_StateMWait) for Buck_StateMWait
    core.o(i.Buck_StateM) refers to core.o(i.Buck_StateMRun) for Buck_StateMRun
    core.o(i.Buck_StateM) refers to core.o(i.Buck_StateMErr) for Buck_StateMErr
    core.o(i.Buck_StateM) refers to core.o(.data) for DF
    core.o(i.Buck_StateMErr) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.Buck_StateMErr) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.Buck_StateMErr) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.Buck_StateMErr) refers to core.o(.data) for DF
    core.o(i.Buck_StateMErr) refers to core.o(.bss) for IO_Para
    core.o(i.Buck_StateMInit) refers to core.o(i.Buck_ValInit) for Buck_ValInit
    core.o(i.Buck_StateMInit) refers to core.o(.data) for DF
    core.o(i.Buck_StateMWait) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    core.o(i.Buck_StateMWait) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    core.o(i.Buck_StateMWait) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    core.o(i.Buck_StateMWait) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    core.o(i.Buck_StateMWait) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    core.o(i.Buck_StateMWait) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    core.o(i.Buck_StateMWait) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    core.o(i.Buck_StateMWait) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    core.o(i.Buck_StateMWait) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.Buck_StateMWait) refers to delay.o(i.delay_ms) for delay_ms
    core.o(i.Buck_StateMWait) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.Buck_StateMWait) refers to core.o(.data) for DF
    core.o(i.Buck_StateMWait) refers to core.o(.bss) for IO_Para
    core.o(i.Buck_StateMWait) refers to adc.o(.bss) for ADCConvertedValue
    core.o(i.Buck_ValInit) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.Buck_ValInit) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.Buck_ValInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.Buck_ValInit) refers to core.o(.data) for DF
    core.o(i.Buck_ValInit) refers to core.o(.bss) for IO_Para
    core.o(i.Cal_IO_Para) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    core.o(i.Cal_IO_Para) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    core.o(i.Cal_IO_Para) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    core.o(i.Cal_IO_Para) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    core.o(i.Cal_IO_Para) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    core.o(i.Cal_IO_Para) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    core.o(i.Cal_IO_Para) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    core.o(i.Cal_IO_Para) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    core.o(i.Cal_IO_Para) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    core.o(i.Cal_IO_Para) refers to adc.o(.bss) for ADCConvertedValue
    core.o(i.Cal_IO_Para) refers to core.o(.bss) for IO_Para
    core.o(i.PWM_Adjust) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.PWM_Adjust) refers to core.o(.data) for Set_Para
    core.o(i.PWM_Adjust) refers to core.o(.bss) for IO_Para
    core.o(i.ShortOff) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.ShortOff) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.ShortOff) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.ShortOff) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.ShortOff) refers to core.o(.bss) for IO_Para
    core.o(i.ShortOff) refers to core.o(.data) for DF
    core.o(i.SwOCP) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.SwOCP) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.SwOCP) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.SwOCP) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.SwOCP) refers to core.o(.bss) for IO_Para
    core.o(i.SwOCP) refers to core.o(.data) for DF
    core.o(i.VinSwOVP) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.VinSwOVP) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.VinSwOVP) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.VinSwOVP) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.VinSwOVP) refers to core.o(.bss) for IO_Para
    core.o(i.VinSwOVP) refers to core.o(.data) for OVPCnt
    core.o(i.VinSwUVP) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.VinSwUVP) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.VinSwUVP) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.VinSwUVP) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.VinSwUVP) refers to core.o(.bss) for IO_Para
    core.o(i.VinSwUVP) refers to core.o(.data) for DF
    core.o(i.VoutOverpower) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.VoutOverpower) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.VoutOverpower) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.VoutOverpower) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.VoutOverpower) refers to core.o(.bss) for IO_Para
    core.o(i.VoutOverpower) refers to core.o(.data) for OVerPCnt
    core.o(i.VoutSwOVP) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    core.o(i.VoutSwOVP) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    core.o(i.VoutSwOVP) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    core.o(i.VoutSwOVP) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    core.o(i.VoutSwOVP) refers to core.o(.bss) for IO_Para
    core.o(i.VoutSwOVP) refers to core.o(.data) for OVPCnt
    pwm.o(i.BUCK_SDIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.BUCK_SDIO_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    pwm.o(i.BUCK_SDIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.BUCK_SDIO_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_DeInit) for TIM_DeInit
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    oled.o(i.IIC_Start) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Start) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Stop) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Wait_Ack) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Wait_Ack) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(.data) for Hzk
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Data) for Write_IIC_Data
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Command) for Write_IIC_Command
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Start) for IIC_Start
    oled.o(i.Write_IIC_Command) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Stop) for IIC_Stop
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Start) for IIC_Start
    oled.o(i.Write_IIC_Data) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Stop) for IIC_Stop
    oled.o(i.fill_picture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    display.o(i.Display_Blink_Dynamic_Vars) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    display.o(i.Display_Blink_Dynamic_Vars) refers to display.o(i.Para_Split) for Para_Split
    display.o(i.Display_Blink_Dynamic_Vars) refers to core.o(.data) for Set_Para
    display.o(i.Display_Heart) refers to oled.o(i.OLED_Clear) for OLED_Clear
    display.o(i.Display_Heart) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    display.o(i.Display_Normal_Dynamic_Vars) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    display.o(i.Display_Normal_Dynamic_Vars) refers to display.o(i.Para_Split) for Para_Split
    display.o(i.Display_Normal_Dynamic_Vars) refers to core.o(.data) for Set_Para
    display.o(i.Normal_Display) refers to display.o(i.Display_Blink_Dynamic_Vars) for Display_Blink_Dynamic_Vars
    display.o(i.Normal_Display) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    display.o(i.Normal_Display) refers to display.o(i.Display_Normal_Dynamic_Vars) for Display_Normal_Dynamic_Vars
    display.o(i.Normal_Display) refers to display.o(i.Para_Split) for Para_Split
    display.o(i.Normal_Display) refers to core.o(.data) for DF
    display.o(i.Normal_Display) refers to display.o(.data) for cur_position
    display.o(i.Normal_Display) refers to core.o(.bss) for IO_Para
    encoder.o(i.EXTI1_IRQHandler) refers to delay.o(i.delay_ms) for delay_ms
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI1_IRQHandler) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    encoder.o(i.EXTI1_IRQHandler) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    encoder.o(i.EXTI1_IRQHandler) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI1_IRQHandler) refers to core.o(.data) for DF
    encoder.o(i.EXTI1_IRQHandler) refers to display.o(.data) for set_button
    encoder.o(i.EXTI1_IRQHandler) refers to core.o(.bss) for IO_Para
    encoder.o(i.EXTIX_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.EXTIX_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(i.EXTIX_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    encoder.o(i.EXTIX_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.Encoder) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.Encoder) refers to encoder.o(.data) for Enc0
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(i.EXTIX_Init) for EXTIX_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(i.TIM3_Int_Init) for TIM3_Int_Init
    encoder.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(i.TIM3_IRQHandler) refers to encoder.o(i.Encoder) for Encoder
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.ShortOff) for ShortOff
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.SwOCP) for SwOCP
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.VoutSwOVP) for VoutSwOVP
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.VinSwUVP) for VinSwUVP
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.VinSwOVP) for VinSwOVP
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.VoutOverpower) for VoutOverpower
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.Buck_StateM) for Buck_StateM
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.Cal_IO_Para) for Cal_IO_Para
    encoder.o(i.TIM3_IRQHandler) refers to core.o(i.PWM_Adjust) for PWM_Adjust
    encoder.o(i.TIM3_IRQHandler) refers to encoder.o(.data) for num
    encoder.o(i.TIM3_IRQHandler) refers to core.o(.data) for OLEDShowCnt
    encoder.o(i.TIM3_IRQHandler) refers to display.o(.data) for blink_flash_time_count
    encoder.o(i.TIM3_Int_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.TIM3_Int_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_ld.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing delay.o(i.delay_us), (76 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(i.uart_init), (160 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (216 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing adc.o(i.ADC1_IRQHandler), (28 bytes).
    Removing oled.o(i.Delay_1ms), (26 bytes).
    Removing oled.o(i.Delay_50ms), (22 bytes).
    Removing oled.o(i.OLED_Display_Off), (28 bytes).
    Removing oled.o(i.OLED_Display_On), (28 bytes).
    Removing oled.o(i.OLED_DrawBMP), (118 bytes).
    Removing oled.o(i.OLED_On), (62 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (104 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.fill_picture), (64 bytes).
    Removing oled.o(i.oled_pow), (22 bytes).
    Removing oled.o(.data), (1024 bytes).

450 unused section(s) (total 18402 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_ld.s           0x00000000   Number         0  startup_stm32f10x_ld.o ABSOLUTE
    ..\HARDWARE\ENCODER\encoder.c            0x00000000   Number         0  encoder.o ABSOLUTE
    ..\HARDWARE\PWM\pwm.c                    0x00000000   Number         0  pwm.o ABSOLUTE
    ..\HARDWARE\adc\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\core\core.c                  0x00000000   Number         0  core.o ABSOLUTE
    ..\HARDWARE\display\display.c            0x00000000   Number         0  display.o ABSOLUTE
    ..\HARDWARE\oled\oled.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_bkp.c   0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_can.c   0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_cec.c   0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_crc.c   0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dac.c   0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_fsmc.c  0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_i2c.c   0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_pwr.c   0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rtc.c   0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_sdio.c  0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_spi.c   0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_wwdg.c  0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_ld.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       56  startup_stm32f10x_ld.o(.text)
    .text                                    0x080001c0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080001c2   Section        0  heapauxi.o(.text)
    .text                                    0x080001c8   Section        2  use_no_semi.o(.text)
    .text                                    0x080001ca   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000214   Section        0  exit.o(.text)
    .text                                    0x08000228   Section        8  libspace.o(.text)
    i.ADC_Cmd                                0x08000230   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_DMACmd                             0x08000246   Section        0  stm32f10x_adc.o(i.ADC_DMACmd)
    i.ADC_GetCalibrationStatus               0x0800025c   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetResetCalibrationStatus          0x08000270   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x08000284   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080002d4   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x0800038c   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x08000396   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x080003ac   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.ADCx_InitConfig                        0x080003b8   Section        0  adc.o(i.ADCx_InitConfig)
    i.BUCK_SDIO_Init                         0x080004ac   Section        0  pwm.o(i.BUCK_SDIO_Init)
    i.Buck_StateM                            0x080004f0   Section        0  core.o(i.Buck_StateM)
    i.Buck_StateMErr                         0x08000528   Section        0  core.o(i.Buck_StateMErr)
    i.Buck_StateMInit                        0x08000570   Section        0  core.o(i.Buck_StateMInit)
    i.Buck_StateMRun                         0x08000584   Section        0  core.o(i.Buck_StateMRun)
    i.Buck_StateMWait                        0x08000588   Section        0  core.o(i.Buck_StateMWait)
    i.Buck_ValInit                           0x080006dc   Section        0  core.o(i.Buck_ValInit)
    i.BusFault_Handler                       0x0800071c   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Cal_IO_Para                            0x08000720   Section        0  core.o(i.Cal_IO_Para)
    i.DMA_Cmd                                0x08000a4c   Section        0  stm32f10x_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x08000a64   Section        0  stm32f10x_dma.o(i.DMA_DeInit)
    i.DMA_Init                               0x08000bb0   Section        0  stm32f10x_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x08000bec   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Display_Blink_Dynamic_Vars             0x08000bf0   Section        0  display.o(i.Display_Blink_Dynamic_Vars)
    i.Display_Heart                          0x08000c7c   Section        0  display.o(i.Display_Heart)
    i.Display_Normal_Dynamic_Vars            0x08000cf4   Section        0  display.o(i.Display_Normal_Dynamic_Vars)
    i.EXTI1_IRQHandler                       0x08000d80   Section        0  encoder.o(i.EXTI1_IRQHandler)
    i.EXTIX_Init                             0x08000e80   Section        0  encoder.o(i.EXTIX_Init)
    i.EXTI_ClearITPendingBit                 0x08000ecc   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_Init                              0x08000ed8   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.Encoder                                0x08000f6c   Section        0  encoder.o(i.Encoder)
    i.Encoder_Init                           0x080010bc   Section        0  encoder.o(i.Encoder_Init)
    i.GPIO_Configuration                     0x08001108   Section        0  adc.o(i.GPIO_Configuration)
    i.GPIO_EXTILineConfig                    0x08001124   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08001164   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x0800127c   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ReadInputDataBit                  0x0800130c   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x0800131e   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001322   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08001326   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.IIC_Start                              0x0800132c   Section        0  oled.o(i.IIC_Start)
    i.IIC_Stop                               0x08001358   Section        0  oled.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x0800137c   Section        0  oled.o(i.IIC_Wait_Ack)
    i.MemManage_Handler                      0x08001398   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800139c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080013a0   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001410   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.Normal_Display                         0x08001424   Section        0  display.o(i.Normal_Display)
    i.OLED_Clear                             0x08001558   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08001598   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Pos                           0x080016b0   Section        0  oled.o(i.OLED_Set_Pos)
    i.OLED_ShowChar                          0x080016d8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x0800177c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x080017b6   Section        0  oled.o(i.OLED_WR_Byte)
    i.PWM_Adjust                             0x080017d0   Section        0  core.o(i.PWM_Adjust)
    i.Para_Split                             0x080019b8   Section        0  display.o(i.Para_Split)
    i.PendSV_Handler                         0x080019ee   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x080019f0   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_AHBPeriphClockCmd                  0x08001a08   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08001a28   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x08001a48   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001a68   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08001a88   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_Configuration                      0x08001aa8   Section        0  adc.o(i.RCC_Configuration)
    i.SVC_Handler                            0x08001ac6   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001ac8   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001ac9   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001ad0   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001ad1   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.ShortOff                               0x08001bb0   Section        0  core.o(i.ShortOff)
    i.SwOCP                                  0x08001c88   Section        0  core.o(i.SwOCP)
    i.SysTick_CLKSourceConfig                0x08001d80   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08001da8   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001dac   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM1_PWM_Init                          0x08001e0c   Section        0  pwm.o(i.TIM1_PWM_Init)
    i.TIM3_IRQHandler                        0x08001f20   Section        0  encoder.o(i.TIM3_IRQHandler)
    i.TIM3_Int_Init                          0x08002184   Section        0  encoder.o(i.TIM3_Int_Init)
    i.TIM_ARRPreloadConfig                   0x080021e8   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_BDTRConfig                         0x08002200   Section        0  stm32f10x_tim.o(i.TIM_BDTRConfig)
    i.TIM_ClearITPendingBit                  0x08002220   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08002226   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x0800223e   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_DeInit                             0x0800225c   Section        0  stm32f10x_tim.o(i.TIM_DeInit)
    i.TIM_GetITStatus                        0x08002444   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08002466   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_InternalClockConfig                0x08002478   Section        0  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    i.TIM_OC1Init                            0x08002484   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x0800251c   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_SetCompare1                        0x0800252e   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_TimeBaseInit                       0x08002534   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x080025d8   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_GetITStatus                      0x08002660   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ReceiveData                      0x080026b4   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x080026be   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.VinSwOVP                               0x080026c4   Section        0  core.o(i.VinSwOVP)
    i.VinSwUVP                               0x08002740   Section        0  core.o(i.VinSwUVP)
    i.VoutOverpower                          0x0800280c   Section        0  core.o(i.VoutOverpower)
    i.VoutSwOVP                              0x08002884   Section        0  core.o(i.VoutSwOVP)
    i.Write_IIC_Byte                         0x08002900   Section        0  oled.o(i.Write_IIC_Byte)
    i.Write_IIC_Command                      0x0800295c   Section        0  oled.o(i.Write_IIC_Command)
    i.Write_IIC_Data                         0x08002988   Section        0  oled.o(i.Write_IIC_Data)
    i._sys_exit                              0x080029b4   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x080029b8   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080029fc   Section        0  delay.o(i.delay_ms)
    i.main                                   0x08002a48   Section        0  main.o(i.main)
    x$fpl$d2f                                0x08002a94   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08002af8   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08002b09   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08002c48   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08002c60   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08002c67   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dflt                               0x08002f10   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dleqf                              0x08002f40   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08002fb8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800310c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080031a8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$dsub                               0x080031b4   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080031c5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08003388   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fcmpinf                            0x080033de   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x080033f8   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080033f9   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x0800357c   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x080035b4   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fleqf                              0x080035e4   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x0800364c   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x0800374e   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080037da   Section       10  fretinf.o(x$fpl$fretinf)
    .constdata                               0x080037e4   Section     2072  oled.o(.constdata)
    x$fpl$usenofp                            0x080037e4   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section        4  delay.o(.data)
    fac_us                                   0x20000014   Data           1  delay.o(.data)
    fac_ms                                   0x20000016   Data           2  delay.o(.data)
    .data                                    0x20000018   Section        6  usart.o(.data)
    .data                                    0x20000020   Section       74  core.o(.data)
    Buck_CntS                                0x20000046   Data           2  core.o(.data)
    IoutSum                                  0x20000048   Data           4  core.o(.data)
    IinSum                                   0x2000004c   Data           4  core.o(.data)
    middle_val                               0x20000050   Data           4  core.o(.data)
    RSCnt                                    0x20000054   Data           4  core.o(.data)
    RSNum                                    0x20000058   Data           1  core.o(.data)
    OCPCnt                                   0x2000005a   Data           2  core.o(.data)
    RSCnt                                    0x2000005c   Data           2  core.o(.data)
    RSNum                                    0x2000005e   Data           2  core.o(.data)
    OVPCnt                                   0x20000060   Data           2  core.o(.data)
    UVPCnt                                   0x20000062   Data           2  core.o(.data)
    RSCnt                                    0x20000064   Data           2  core.o(.data)
    OVPCnt                                   0x20000066   Data           2  core.o(.data)
    OVerPCnt                                 0x20000068   Data           2  core.o(.data)
    .data                                    0x2000006c   Section        8  display.o(.data)
    .data                                    0x20000074   Section       16  encoder.o(.data)
    Enc0                                     0x2000007a   Data           1  encoder.o(.data)
    Enc1                                     0x2000007b   Data           1  encoder.o(.data)
    EncOld                                   0x2000007c   Data           1  encoder.o(.data)
    EncX                                     0x2000007d   Data           1  encoder.o(.data)
    num                                      0x20000080   Data           4  encoder.o(.data)
    .bss                                     0x20000084   Section      200  usart.o(.bss)
    .bss                                     0x2000014c   Section     2400  adc.o(.bss)
    .bss                                     0x20000aac   Section       84  core.o(.bss)
    .bss                                     0x20000b00   Section       96  libspace.o(.bss)
    HEAP                                     0x20000b60   Section      512  startup_stm32f10x_ld.o(HEAP)
    Heap_Mem                                 0x20000b60   Data         512  startup_stm32f10x_ld.o(HEAP)
    STACK                                    0x20000d60   Section     1024  startup_stm32f10x_ld.o(STACK)
    Stack_Mem                                0x20000d60   Data        1024  startup_stm32f10x_ld.o(STACK)
    __initial_sp                             0x20001160   Data           0  startup_stm32f10x_ld.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_ld.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_ld.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_ld.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     4  startup_stm32f10x_ld.o(.text)
    ADC1_2_IRQHandler                        0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    CAN1_RX1_IRQHandler                      0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    CAN1_SCE_IRQHandler                      0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI0_IRQHandler                         0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI15_10_IRQHandler                     0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI2_IRQHandler                         0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI3_IRQHandler                         0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI4_IRQHandler                         0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    EXTI9_5_IRQHandler                       0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    FLASH_IRQHandler                         0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    I2C1_ER_IRQHandler                       0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    I2C1_EV_IRQHandler                       0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    PVD_IRQHandler                           0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    RCC_IRQHandler                           0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    RTCAlarm_IRQHandler                      0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    RTC_IRQHandler                           0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    SPI1_IRQHandler                          0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TAMPER_IRQHandler                        0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TIM1_BRK_IRQHandler                      0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TIM1_CC_IRQHandler                       0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TIM1_UP_IRQHandler                       0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    TIM2_IRQHandler                          0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    USART2_IRQHandler                        0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    USBWakeUp_IRQHandler                     0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    WWDG_IRQHandler                          0x0800019f   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    __user_initial_stackheap                 0x080001a1   Thumb Code     0  startup_stm32f10x_ld.o(.text)
    __use_no_semihosting                     0x080001c1   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x080001c3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001c7   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080001c9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080001c9   Thumb Code     2  use_no_semi.o(.text)
    __user_setup_stackheap                   0x080001cb   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000215   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000229   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000229   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000229   Thumb Code     0  libspace.o(.text)
    ADC_Cmd                                  0x08000231   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_DMACmd                               0x08000247   Thumb Code    22  stm32f10x_adc.o(i.ADC_DMACmd)
    ADC_GetCalibrationStatus                 0x0800025d   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetResetCalibrationStatus            0x08000271   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x08000285   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080002d5   Thumb Code   184  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x0800038d   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x08000397   Thumb Code    22  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x080003ad   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    ADCx_InitConfig                          0x080003b9   Thumb Code   232  adc.o(i.ADCx_InitConfig)
    BUCK_SDIO_Init                           0x080004ad   Thumb Code    58  pwm.o(i.BUCK_SDIO_Init)
    Buck_StateM                              0x080004f1   Thumb Code    50  core.o(i.Buck_StateM)
    Buck_StateMErr                           0x08000529   Thumb Code    54  core.o(i.Buck_StateMErr)
    Buck_StateMInit                          0x08000571   Thumb Code    14  core.o(i.Buck_StateMInit)
    Buck_StateMRun                           0x08000585   Thumb Code     2  core.o(i.Buck_StateMRun)
    Buck_StateMWait                          0x08000589   Thumb Code   292  core.o(i.Buck_StateMWait)
    Buck_ValInit                             0x080006dd   Thumb Code    46  core.o(i.Buck_ValInit)
    BusFault_Handler                         0x0800071d   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Cal_IO_Para                              0x08000721   Thumb Code   758  core.o(i.Cal_IO_Para)
    DMA_Cmd                                  0x08000a4d   Thumb Code    24  stm32f10x_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x08000a65   Thumb Code   324  stm32f10x_dma.o(i.DMA_DeInit)
    DMA_Init                                 0x08000bb1   Thumb Code    60  stm32f10x_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x08000bed   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Display_Blink_Dynamic_Vars               0x08000bf1   Thumb Code   116  display.o(i.Display_Blink_Dynamic_Vars)
    Display_Heart                            0x08000c7d   Thumb Code    56  display.o(i.Display_Heart)
    Display_Normal_Dynamic_Vars              0x08000cf5   Thumb Code   116  display.o(i.Display_Normal_Dynamic_Vars)
    EXTI1_IRQHandler                         0x08000d81   Thumb Code   228  encoder.o(i.EXTI1_IRQHandler)
    EXTIX_Init                               0x08000e81   Thumb Code    76  encoder.o(i.EXTIX_Init)
    EXTI_ClearITPendingBit                   0x08000ecd   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_Init                                0x08000ed9   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    Encoder                                  0x08000f6d   Thumb Code   312  encoder.o(i.Encoder)
    Encoder_Init                             0x080010bd   Thumb Code    66  encoder.o(i.Encoder_Init)
    GPIO_Configuration                       0x08001109   Thumb Code    24  adc.o(i.GPIO_Configuration)
    GPIO_EXTILineConfig                      0x08001125   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08001165   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x0800127d   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ReadInputDataBit                    0x0800130d   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x0800131f   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001323   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08001327   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    IIC_Start                                0x0800132d   Thumb Code    40  oled.o(i.IIC_Start)
    IIC_Stop                                 0x08001359   Thumb Code    30  oled.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x0800137d   Thumb Code    24  oled.o(i.IIC_Wait_Ack)
    MemManage_Handler                        0x08001399   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800139d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080013a1   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001411   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    Normal_Display                           0x08001425   Thumb Code   266  display.o(i.Normal_Display)
    OLED_Clear                               0x08001559   Thumb Code    62  oled.o(i.OLED_Clear)
    OLED_Init                                0x08001599   Thumb Code   274  oled.o(i.OLED_Init)
    OLED_Set_Pos                             0x080016b1   Thumb Code    40  oled.o(i.OLED_Set_Pos)
    OLED_ShowChar                            0x080016d9   Thumb Code   154  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x0800177d   Thumb Code    58  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x080017b7   Thumb Code    24  oled.o(i.OLED_WR_Byte)
    PWM_Adjust                               0x080017d1   Thumb Code   472  core.o(i.PWM_Adjust)
    Para_Split                               0x080019b9   Thumb Code    54  display.o(i.Para_Split)
    PendSV_Handler                           0x080019ef   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x080019f1   Thumb Code    18  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_AHBPeriphClockCmd                    0x08001a09   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08001a29   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x08001a49   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x08001a69   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08001a89   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_Configuration                        0x08001aa9   Thumb Code    30  adc.o(i.RCC_Configuration)
    SVC_Handler                              0x08001ac7   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    ShortOff                                 0x08001bb1   Thumb Code   192  core.o(i.ShortOff)
    SwOCP                                    0x08001c89   Thumb Code   218  core.o(i.SwOCP)
    SysTick_CLKSourceConfig                  0x08001d81   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08001da9   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08001dad   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM1_PWM_Init                            0x08001e0d   Thumb Code   262  pwm.o(i.TIM1_PWM_Init)
    TIM3_IRQHandler                          0x08001f21   Thumb Code   562  encoder.o(i.TIM3_IRQHandler)
    TIM3_Int_Init                            0x08002185   Thumb Code    94  encoder.o(i.TIM3_Int_Init)
    TIM_ARRPreloadConfig                     0x080021e9   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_BDTRConfig                           0x08002201   Thumb Code    32  stm32f10x_tim.o(i.TIM_BDTRConfig)
    TIM_ClearITPendingBit                    0x08002221   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08002227   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x0800223f   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_DeInit                               0x0800225d   Thumb Code   424  stm32f10x_tim.o(i.TIM_DeInit)
    TIM_GetITStatus                          0x08002445   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08002467   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_InternalClockConfig                  0x08002479   Thumb Code    12  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    TIM_OC1Init                              0x08002485   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x0800251d   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_SetCompare1                          0x0800252f   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_TimeBaseInit                         0x08002535   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x080025d9   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART_GetITStatus                        0x08002661   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ReceiveData                        0x080026b5   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x080026bf   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    VinSwOVP                                 0x080026c5   Thumb Code   102  core.o(i.VinSwOVP)
    VinSwUVP                                 0x08002741   Thumb Code   180  core.o(i.VinSwUVP)
    VoutOverpower                            0x0800280d   Thumb Code   100  core.o(i.VoutOverpower)
    VoutSwOVP                                0x08002885   Thumb Code   102  core.o(i.VoutSwOVP)
    Write_IIC_Byte                           0x08002901   Thumb Code    86  oled.o(i.Write_IIC_Byte)
    Write_IIC_Command                        0x0800295d   Thumb Code    44  oled.o(i.Write_IIC_Command)
    Write_IIC_Data                           0x08002989   Thumb Code    44  oled.o(i.Write_IIC_Data)
    _sys_exit                                0x080029b5   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x080029b9   Thumb Code    50  delay.o(i.delay_init)
    delay_ms                                 0x080029fd   Thumb Code    72  delay.o(i.delay_ms)
    main                                     0x08002a49   Thumb Code    70  main.o(i.main)
    __aeabi_d2f                              0x08002a95   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08002a95   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08002af9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08002af9   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08002c49   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08002c61   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002c61   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_i2d                              0x08002f11   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08002f11   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_cdcmple                          0x08002f41   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08002f41   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08002fa3   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08002fb9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002fb9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800310d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080031a9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_dsub                             0x080031b5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080031b5   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08003389   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08003389   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fcmp_Inf                           0x080033df   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080033f9   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080033f9   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x0800357d   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x0800357d   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x080035b5   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080035b5   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_cfcmple                          0x080035e5   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080035e5   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08003637   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x0800364d   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x0800364d   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x0800374f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080037db   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    F6x8                                     0x080037e4   Data         552  oled.o(.constdata)
    __I$use$fp                               0x080037e4   Number         0  usenofp.o(x$fpl$usenofp)
    F8X16                                    0x08003a0c   Data        1520  oled.o(.constdata)
    Region$$Table$$Base                      0x08003ffc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800401c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000018   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000001c   Data           2  usart.o(.data)
    Set_Para                                 0x20000020   Data          24  core.o(.data)
    DF                                       0x20000038   Data           8  core.o(.data)
    OLEDShowCnt                              0x20000040   Data           2  core.o(.data)
    Sys_Check_Cnt                            0x20000042   Data           2  core.o(.data)
    FIRST_IN_FLAG                            0x20000044   Data           1  core.o(.data)
    set_button                               0x2000006c   Data           1  display.o(.data)
    blink_flash_time_count                   0x2000006e   Data           2  display.o(.data)
    cur_position                             0x20000070   Data           4  display.o(.data)
    enc                                      0x20000074   Data           1  encoder.o(.data)
    count                                    0x20000076   Data           2  encoder.o(.data)
    PWM_Time_Count                           0x20000078   Data           2  encoder.o(.data)
    USART_RX_BUF                             0x20000084   Data         200  usart.o(.bss)
    ADCConvertedValue                        0x2000014c   Data        2400  adc.o(.bss)
    ADVal_SUM                                0x20000aac   Data          16  core.o(.bss)
    ADVal_Avg                                0x20000abc   Data          16  core.o(.bss)
    MPPT_Con_Para                            0x20000acc   Data          16  core.o(.bss)
    IO_Para                                  0x20000adc   Data          36  core.o(.bss)
    __libspace_start                         0x20000b00   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000b60   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_1 (Base: 0x08000000, Size: 0x000040a0, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Base: 0x08000000, Size: 0x0000401c, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO          370    RESET               startup_stm32f10x_ld.o
    0x080000ec   0x00000008   Code   RO         3739  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         3949    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000001a   Code   RO         3951    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x00000002   PAD
    0x08000144   0x0000001c   Code   RO         3953    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x00000002   Code   RO         3823    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x00000000   Code   RO         3830    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3832    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3835    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3837    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3839    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3842    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3844    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3846    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3848    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3850    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3852    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3854    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3856    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3858    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3860    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3862    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3866    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3868    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3870    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         3872    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x00000002   Code   RO         3873    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x00000002   Code   RO         3891    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x00000000   Code   RO         3901    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         3903    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         3906    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         3909    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         3911    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         3914    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000166   0x00000002   Code   RO         3915    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000168   0x00000000   Code   RO         3785    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x00000000   Code   RO         3800    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x00000006   Code   RO         3812    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x00000000   Code   RO         3802    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x00000004   Code   RO         3803    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x00000000   Code   RO         3805    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x00000008   Code   RO         3806    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x00000002   Code   RO         3827    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x00000000   Code   RO         3875    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x00000004   Code   RO         3876    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x00000006   Code   RO         3877    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x00000038   Code   RO          371  * .text               startup_stm32f10x_ld.o
    0x080001c0   0x00000002   Code   RO         3735    .text               c_w.l(use_no_semi_2.o)
    0x080001c2   0x00000006   Code   RO         3737    .text               c_w.l(heapauxi.o)
    0x080001c8   0x00000002   Code   RO         3783    .text               c_w.l(use_no_semi.o)
    0x080001ca   0x0000004a   Code   RO         3814    .text               c_w.l(sys_stackheap_outer.o)
    0x08000214   0x00000012   Code   RO         3816    .text               c_w.l(exit.o)
    0x08000226   0x00000002   PAD
    0x08000228   0x00000008   Code   RO         3824    .text               c_w.l(libspace.o)
    0x08000230   0x00000016   Code   RO          417    i.ADC_Cmd           stm32f10x_adc.o
    0x08000246   0x00000016   Code   RO          418    i.ADC_DMACmd        stm32f10x_adc.o
    0x0800025c   0x00000014   Code   RO          425    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x08000270   0x00000014   Code   RO          431    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x08000284   0x00000050   Code   RO          435    i.ADC_Init          stm32f10x_adc.o
    0x080002d4   0x000000b8   Code   RO          439    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x0800038c   0x0000000a   Code   RO          440    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x08000396   0x00000016   Code   RO          442    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x080003ac   0x0000000a   Code   RO          444    i.ADC_StartCalibration  stm32f10x_adc.o
    0x080003b6   0x00000002   PAD
    0x080003b8   0x000000f4   Code   RO         3343    i.ADCx_InitConfig   adc.o
    0x080004ac   0x00000044   Code   RO         3480    i.BUCK_SDIO_Init    pwm.o
    0x080004f0   0x00000038   Code   RO         3378    i.Buck_StateM       core.o
    0x08000528   0x00000048   Code   RO         3379    i.Buck_StateMErr    core.o
    0x08000570   0x00000014   Code   RO         3380    i.Buck_StateMInit   core.o
    0x08000584   0x00000002   Code   RO         3381    i.Buck_StateMRun    core.o
    0x08000586   0x00000002   PAD
    0x08000588   0x00000154   Code   RO         3382    i.Buck_StateMWait   core.o
    0x080006dc   0x00000040   Code   RO         3383    i.Buck_ValInit      core.o
    0x0800071c   0x00000004   Code   RO          139    i.BusFault_Handler  stm32f10x_it.o
    0x08000720   0x0000032c   Code   RO         3384    i.Cal_IO_Para       core.o
    0x08000a4c   0x00000018   Code   RO         1102    i.DMA_Cmd           stm32f10x_dma.o
    0x08000a64   0x0000014c   Code   RO         1103    i.DMA_DeInit        stm32f10x_dma.o
    0x08000bb0   0x0000003c   Code   RO         1108    i.DMA_Init          stm32f10x_dma.o
    0x08000bec   0x00000002   Code   RO          140    i.DebugMon_Handler  stm32f10x_it.o
    0x08000bee   0x00000002   PAD
    0x08000bf0   0x0000008c   Code   RO         3646    i.Display_Blink_Dynamic_Vars  display.o
    0x08000c7c   0x00000078   Code   RO         3647    i.Display_Heart     display.o
    0x08000cf4   0x0000008c   Code   RO         3648    i.Display_Normal_Dynamic_Vars  display.o
    0x08000d80   0x00000100   Code   RO         3688    i.EXTI1_IRQHandler  encoder.o
    0x08000e80   0x0000004c   Code   RO         3689    i.EXTIX_Init        encoder.o
    0x08000ecc   0x0000000c   Code   RO         1173    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000ed8   0x00000094   Code   RO         1178    i.EXTI_Init         stm32f10x_exti.o
    0x08000f6c   0x00000150   Code   RO         3690    i.Encoder           encoder.o
    0x080010bc   0x0000004c   Code   RO         3691    i.Encoder_Init      encoder.o
    0x08001108   0x0000001c   Code   RO         3344    i.GPIO_Configuration  adc.o
    0x08001124   0x00000040   Code   RO         1523    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08001164   0x00000116   Code   RO         1526    i.GPIO_Init         stm32f10x_gpio.o
    0x0800127a   0x00000002   PAD
    0x0800127c   0x00000090   Code   RO         1528    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x0800130c   0x00000012   Code   RO         1530    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x0800131e   0x00000004   Code   RO         1533    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08001322   0x00000004   Code   RO         1534    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001326   0x00000004   Code   RO          141    i.HardFault_Handler  stm32f10x_it.o
    0x0800132a   0x00000002   PAD
    0x0800132c   0x0000002c   Code   RO         3500    i.IIC_Start         oled.o
    0x08001358   0x00000024   Code   RO         3501    i.IIC_Stop          oled.o
    0x0800137c   0x0000001c   Code   RO         3502    i.IIC_Wait_Ack      oled.o
    0x08001398   0x00000004   Code   RO          142    i.MemManage_Handler  stm32f10x_it.o
    0x0800139c   0x00000002   Code   RO          143    i.NMI_Handler       stm32f10x_it.o
    0x0800139e   0x00000002   PAD
    0x080013a0   0x00000070   Code   RO          375    i.NVIC_Init         misc.o
    0x08001410   0x00000014   Code   RO          376    i.NVIC_PriorityGroupConfig  misc.o
    0x08001424   0x00000134   Code   RO         3649    i.Normal_Display    display.o
    0x08001558   0x0000003e   Code   RO         3503    i.OLED_Clear        oled.o
    0x08001596   0x00000002   PAD
    0x08001598   0x00000118   Code   RO         3507    i.OLED_Init         oled.o
    0x080016b0   0x00000028   Code   RO         3509    i.OLED_Set_Pos      oled.o
    0x080016d8   0x000000a4   Code   RO         3511    i.OLED_ShowChar     oled.o
    0x0800177c   0x0000003a   Code   RO         3513    i.OLED_ShowString   oled.o
    0x080017b6   0x00000018   Code   RO         3514    i.OLED_WR_Byte      oled.o
    0x080017ce   0x00000002   PAD
    0x080017d0   0x000001e8   Code   RO         3385    i.PWM_Adjust        core.o
    0x080019b8   0x00000036   Code   RO         3650    i.Para_Split        display.o
    0x080019ee   0x00000002   Code   RO          144    i.PendSV_Handler    stm32f10x_it.o
    0x080019f0   0x00000018   Code   RO         1940    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08001a08   0x00000020   Code   RO         1941    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x08001a28   0x00000020   Code   RO         1942    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001a48   0x00000020   Code   RO         1943    i.RCC_APB1PeriphResetCmd  stm32f10x_rcc.o
    0x08001a68   0x00000020   Code   RO         1944    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001a88   0x00000020   Code   RO         1945    i.RCC_APB2PeriphResetCmd  stm32f10x_rcc.o
    0x08001aa8   0x0000001e   Code   RO         3345    i.RCC_Configuration  adc.o
    0x08001ac6   0x00000002   Code   RO          145    i.SVC_Handler       stm32f10x_it.o
    0x08001ac8   0x00000008   Code   RO          228    i.SetSysClock       system_stm32f10x.o
    0x08001ad0   0x000000e0   Code   RO          229    i.SetSysClockTo72   system_stm32f10x.o
    0x08001bb0   0x000000d8   Code   RO         3386    i.ShortOff          core.o
    0x08001c88   0x000000f8   Code   RO         3387    i.SwOCP             core.o
    0x08001d80   0x00000028   Code   RO          379    i.SysTick_CLKSourceConfig  misc.o
    0x08001da8   0x00000002   Code   RO          146    i.SysTick_Handler   stm32f10x_it.o
    0x08001daa   0x00000002   PAD
    0x08001dac   0x00000060   Code   RO          231    i.SystemInit        system_stm32f10x.o
    0x08001e0c   0x00000114   Code   RO         3481    i.TIM1_PWM_Init     pwm.o
    0x08001f20   0x00000264   Code   RO         3692    i.TIM3_IRQHandler   encoder.o
    0x08002184   0x00000064   Code   RO         3693    i.TIM3_Int_Init     encoder.o
    0x080021e8   0x00000018   Code   RO         2564    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08002200   0x00000020   Code   RO         2565    i.TIM_BDTRConfig    stm32f10x_tim.o
    0x08002220   0x00000006   Code   RO         2571    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08002226   0x00000018   Code   RO         2576    i.TIM_Cmd           stm32f10x_tim.o
    0x0800223e   0x0000001e   Code   RO         2578    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x0800225c   0x000001e8   Code   RO         2581    i.TIM_DeInit        stm32f10x_tim.o
    0x08002444   0x00000022   Code   RO         2597    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08002466   0x00000012   Code   RO         2601    i.TIM_ITConfig      stm32f10x_tim.o
    0x08002478   0x0000000c   Code   RO         2603    i.TIM_InternalClockConfig  stm32f10x_tim.o
    0x08002484   0x00000098   Code   RO         2605    i.TIM_OC1Init       stm32f10x_tim.o
    0x0800251c   0x00000012   Code   RO         2608    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x0800252e   0x00000004   Code   RO         2637    i.TIM_SetCompare1   stm32f10x_tim.o
    0x08002532   0x00000002   PAD
    0x08002534   0x000000a4   Code   RO         2647    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x080025d8   0x00000088   Code   RO          317    i.USART1_IRQHandler  usart.o
    0x08002660   0x00000054   Code   RO         3116    i.USART_GetITStatus  stm32f10x_usart.o
    0x080026b4   0x0000000a   Code   RO         3126    i.USART_ReceiveData  stm32f10x_usart.o
    0x080026be   0x00000004   Code   RO          147    i.UsageFault_Handler  stm32f10x_it.o
    0x080026c2   0x00000002   PAD
    0x080026c4   0x0000007c   Code   RO         3388    i.VinSwOVP          core.o
    0x08002740   0x000000cc   Code   RO         3389    i.VinSwUVP          core.o
    0x0800280c   0x00000078   Code   RO         3390    i.VoutOverpower     core.o
    0x08002884   0x0000007c   Code   RO         3391    i.VoutSwOVP         core.o
    0x08002900   0x0000005c   Code   RO         3515    i.Write_IIC_Byte    oled.o
    0x0800295c   0x0000002c   Code   RO         3516    i.Write_IIC_Command  oled.o
    0x08002988   0x0000002c   Code   RO         3517    i.Write_IIC_Data    oled.o
    0x080029b4   0x00000004   Code   RO          318    i._sys_exit         usart.o
    0x080029b8   0x00000044   Code   RO          262    i.delay_init        delay.o
    0x080029fc   0x0000004c   Code   RO          263    i.delay_ms          delay.o
    0x08002a48   0x0000004c   Code   RO            1    i.main              main.o
    0x08002a94   0x00000062   Code   RO         3741    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08002af6   0x00000002   PAD
    0x08002af8   0x00000150   Code   RO         3743    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08002c48   0x00000018   Code   RO         3786    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x08002c60   0x000002b0   Code   RO         3750    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002f10   0x0000002e   Code   RO         3754    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08002f3e   0x00000002   PAD
    0x08002f40   0x00000078   Code   RO         3759    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08002fb8   0x00000154   Code   RO         3761    x$fpl$dmul          fz_ws.l(dmul.o)
    0x0800310c   0x0000009c   Code   RO         3788    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080031a8   0x0000000c   Code   RO         3790    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080031b4   0x000001d4   Code   RO         3745    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08003388   0x00000056   Code   RO         3763    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080033de   0x00000018   Code   RO         3792    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080033f6   0x00000002   PAD
    0x080033f8   0x00000184   Code   RO         3766    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x0800357c   0x00000036   Code   RO         3769    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080035b2   0x00000002   PAD
    0x080035b4   0x00000030   Code   RO         3774    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080035e4   0x00000068   Code   RO         3779    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x0800364c   0x00000102   Code   RO         3781    x$fpl$fmul          fz_ws.l(fmul.o)
    0x0800374e   0x0000008c   Code   RO         3794    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080037da   0x0000000a   Code   RO         3796    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080037e4   0x00000000   Code   RO         3798    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080037e4   0x00000818   Data   RO         3520    .constdata          oled.o
    0x08003ffc   0x00000020   Data   RO         3947    Region$$Table       anon$$obj.o


    Execution Region ER_RW (Base: 0x20000000, Size: 0x00000084, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000014   Data   RW          232    .data               system_stm32f10x.o
    0x20000014   0x00000004   Data   RW          265    .data               delay.o
    0x20000018   0x00000006   Data   RW          322    .data               usart.o
    0x2000001e   0x00000002   PAD
    0x20000020   0x0000004a   Data   RW         3393    .data               core.o
    0x2000006a   0x00000002   PAD
    0x2000006c   0x00000008   Data   RW         3651    .data               display.o
    0x20000074   0x00000010   Data   RW         3694    .data               encoder.o


    Execution Region ER_ZI (Base: 0x20000084, Size: 0x000010dc, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000084   0x000000c8   Zero   RW          321    .bss                usart.o
    0x2000014c   0x00000960   Zero   RW         3346    .bss                adc.o
    0x20000aac   0x00000054   Zero   RW         3392    .bss                core.o
    0x20000b00   0x00000060   Zero   RW         3825    .bss                c_w.l(libspace.o)
    0x20000b60   0x00000200   Zero   RW          369    HEAP                startup_stm32f10x_ld.o
    0x20000d60   0x00000400   Zero   RW          368    STACK               startup_stm32f10x_ld.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       302         16          0          0       2400       2151   adc.o
      2890        308          0         74         84      10569   core.o
       144         22          0          4          0       1160   delay.o
       762        154          0          8          0       3758   display.o
      1456        118          0         16          0       4458   encoder.o
        76          6          0          0          0     263575   main.o
       172         22          0          0          0       2001   misc.o
       916         36       2072          0          0       7810   oled.o
       344         24          0          0          0       1329   pwm.o
        56         22        236          0       1536        776   startup_stm32f10x_ld.o
       390         10          0          0          0       7261   stm32f10x_adc.o
       416          8          0          0          0       3366   stm32f10x_dma.o
       160         12          0          0          0       1461   stm32f10x_exti.o
       512         10          0          0          0       5012   stm32f10x_gpio.o
        26          0          0          0          0       3578   stm32f10x_it.o
       184         36          0          0          0       3311   stm32f10x_rcc.o
      1006        126          0          0          0       8181   stm32f10x_tim.o
        94          0          0          0          0       2692   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       328         28          0         20          0       2017   system_stm32f10x.o
       140         14          0          6        200       2872   usart.o

    ----------------------------------------------------------------------
     10396        <USER>       <GROUP>        132       4220     337370   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       804         16          0          0          0        272   daddsub_clz.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
        46          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        48          0          0          0          0         68   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      3676        <USER>          <GROUP>          0         96       2368   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       262         12          0          0         96        584   c_w.l
      3400        276          0          0          0       1784   fz_ws.l

    ----------------------------------------------------------------------
      3676        <USER>          <GROUP>          0         96       2368   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14072       1260       2340        132       4316     333494   Grand Totals
     14072       1260       2340        132       4316     333494   ELF Image Totals
     14072       1260       2340        132          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16412 (  16.03kB)
    Total RW  Size (RW Data + ZI Data)              4448 (   4.34kB)
    Total ROM Size (Code + RO Data + RW Data)      16544 (  16.16kB)

==============================================================================

