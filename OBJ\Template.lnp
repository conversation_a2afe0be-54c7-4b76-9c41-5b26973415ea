--cpu Cortex-M3
"..\obj\main.o"
"..\obj\stm32f10x_it.o"
"..\obj\system_stm32f10x.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\core_cm3.o"
"..\obj\startup_stm32f10x_ld.o"
"..\obj\misc.o"
"..\obj\stm32f10x_adc.o"
"..\obj\stm32f10x_bkp.o"
"..\obj\stm32f10x_can.o"
"..\obj\stm32f10x_cec.o"
"..\obj\stm32f10x_crc.o"
"..\obj\stm32f10x_dac.o"
"..\obj\stm32f10x_dbgmcu.o"
"..\obj\stm32f10x_dma.o"
"..\obj\stm32f10x_exti.o"
"..\obj\stm32f10x_flash.o"
"..\obj\stm32f10x_fsmc.o"
"..\obj\stm32f10x_gpio.o"
"..\obj\stm32f10x_i2c.o"
"..\obj\stm32f10x_iwdg.o"
"..\obj\stm32f10x_pwr.o"
"..\obj\stm32f10x_rcc.o"
"..\obj\stm32f10x_rtc.o"
"..\obj\stm32f10x_sdio.o"
"..\obj\stm32f10x_spi.o"
"..\obj\stm32f10x_tim.o"
"..\obj\stm32f10x_usart.o"
"..\obj\stm32f10x_wwdg.o"
"..\obj\adc.o"
"..\obj\core.o"
"..\obj\pwm.o"
"..\obj\oled.o"
"..\obj\display.o"
"..\obj\encoder.o"
--ro-base 0x08000000 --entry 0x08000000 --rw-base 0x20000000 --entry Reset_Handler --first __Vectors --strict --summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\Template.map" -o ..\OBJ\Template.axf