..\obj\preserve.o: ..\HARDWARE\preserve\preserve.c
..\obj\preserve.o: ..\USER\stm32f10x.h
..\obj\preserve.o: ..\CORE\core_cm3.h
..\obj\preserve.o: D:\software\work\KEIL5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\preserve.o: ..\USER\system_stm32f10x.h
..\obj\preserve.o: ..\USER\stm32f10x_conf.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_adc.h
..\obj\preserve.o: ..\USER\stm32f10x.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_bkp.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_can.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_cec.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_crc.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_dac.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_dbgmcu.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_dma.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_exti.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_flash.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_fsmc.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_gpio.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_i2c.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_iwdg.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_pwr.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_rcc.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_rtc.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_sdio.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_spi.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_tim.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_usart.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\stm32f10x_wwdg.h
..\obj\preserve.o: ..\STM32F10x_FWLib\inc\misc.h
..\obj\preserve.o: ..\HARDWARE\preserve\preserve.h
..\obj\preserve.o: ..\SYSTEM\sys\sys.h
