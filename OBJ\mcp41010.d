..\obj\mcp41010.o: ..\HARDWARE\MCP41010\MCP41010.c
..\obj\mcp41010.o: ..\HARDWARE\MCP41010\MCP41010.h
..\obj\mcp41010.o: ..\SYSTEM\sys\sys.h
..\obj\mcp41010.o: ..\USER\stm32f10x.h
..\obj\mcp41010.o: ..\CORE\core_cm3.h
..\obj\mcp41010.o: D:\software\work\KEIL5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\mcp41010.o: ..\USER\system_stm32f10x.h
..\obj\mcp41010.o: ..\USER\stm32f10x_conf.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_adc.h
..\obj\mcp41010.o: ..\USER\stm32f10x.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_bkp.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_can.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_cec.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_crc.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_dac.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_dbgmcu.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_dma.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_exti.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_flash.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_fsmc.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_gpio.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_i2c.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_iwdg.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_pwr.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_rcc.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_rtc.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_sdio.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_spi.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_tim.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_usart.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\stm32f10x_wwdg.h
..\obj\mcp41010.o: ..\STM32F10x_FWLib\inc\misc.h
..\obj\mcp41010.o: ..\SYSTEM\delay\delay.h
